import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/ideabook_color_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/utils/logger.dart';

/// Widget for selecting a color for an ideabook
class ColorPicker extends ConsumerWidget {
  /// The ideabook ID to update
  final String? ideabookId;

  /// The current color of the ideabook
  final IdeabookColor currentColor;

  /// Callback when a color is selected
  final Function(IdeabookColor)? onColorSelected;

  /// Constructor
  const ColorPicker({
    super.key,
    this.ideabookId,
    required this.currentColor,
    this.onColorSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get all available colors except the current one
    final allColors = [
      IdeabookColor.red,
      IdeabookColor.yellow,
      IdeabookColor.orange,
      IdeabookColor.green,
      IdeabookColor.blue,
      IdeabookColor.purple,
    ];

    // Filter out the current color
    final colors = allColors.where((color) => color != currentColor).toList();

    return GestureDetector(
      // Add a tap handler to the entire row to handle taps outside the color squares
      onTap: () {
        if (ideabookId != null) {
          // Exit color picking mode when tapping outside the color squares
          ref.read(colorPickingIdeabookIdProvider.notifier).state = null;
        }
      },
      child: Row(
        children: [
          // Current color indicator (not clickable) - narrow strip
          Container(
            width: 8, // Match the narrow strip width
            height: double.infinity, // Full height of the row
            color: VojiTheme.getIdeabookColor(context, currentColor.index),
            // No border decoration
          ),

          // Color options with slide-in animation and padding
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(-1, 0),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: ModalRoute.of(context)?.animation ?? const AlwaysStoppedAnimation(1),
                  curve: Curves.easeOut,
                )),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: colors.map((color) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 16.0),
                      child: GestureDetector(
                        onTap: () {
                          if (ideabookId != null) {
                            // Update the ideabook color using the provider
                            final updateColor = ref.read(updateIdeabookColorProvider);
                            // Log the color selection for debugging
                            Logger.debug('Color selected: ${color.name} for ideabook $ideabookId');
                            updateColor(ideabookId!, color);
                          } else if (onColorSelected != null) {
                            // Call the callback if provided
                            onColorSelected!(color);
                          }
                        },
                        // Prevent tap events from propagating to parent widgets and ensure they're handled here
                        behavior: HitTestBehavior.opaque,
                        child: Container(
                          width: 30,
                          height: 30,
                          color: VojiTheme.getIdeabookColor(context, color.index),
                          // No border decoration for color options
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ],
    ),
    );
  }
}
